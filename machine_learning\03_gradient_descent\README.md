# 梯度下降算法实现

本文件夹包含了梯度下降算法的完整实现，从简单的数学函数到实际的机器学习应用。

## 文件结构

```
03_gradient_descent/
├── README.md                           # 说明文档
├── 01_1d_gradient_descent.py          # 一维函数梯度下降
├── 02_2d_gradient_descent.py          # 二维函数梯度下降
├── 03_bgd_sgd_mbgd_comparison.py      # 三种梯度下降方法比较
├── 04_linear_regression_gradient_descent.py  # 线性回归梯度下降
└── main.py                            # 主演示文件
```

## 文件说明

### 1. 一维函数梯度下降 (`01_1d_gradient_descent.py`)

**目标函数**: `J(θ) = 0.5 * (θ - 0.25)^2`

**功能**:
- 实现基本的一维梯度下降算法
- 可视化优化路径
- 测试不同学习率的影响

**运行方式**:
```bash
python 01_1d_gradient_descent.py
```

### 2. 二维函数梯度下降 (`02_2d_gradient_descent.py`)

**目标函数**: `J(θ1, θ2) = 0.6 * (θ1 + θ2)^2 - θ1 * θ2`

**功能**:
- 实现二维梯度下降算法
- 3D曲面图和等高线图可视化
- 测试不同起始点的影响

**运行方式**:
```bash
python 02_2d_gradient_descent.py
```

### 3. 梯度下降方法比较 (`03_bgd_sgd_mbgd_comparison.py`)

**目标**: 学习线性函数 `y = 5x` 的参数

**功能**:
- 批量梯度下降 (BGD)
- 随机梯度下降 (SGD)  
- 小批量梯度下降 (MBGD)
- 三种方法的性能比较和可视化

**运行方式**:
```bash
python 03_bgd_sgd_mbgd_comparison.py
```

### 4. 线性回归梯度下降 (`04_linear_regression_gradient_descent.py`)

**目标**: 使用梯度下降解决线性回归问题

**功能**:
- 完整的线性回归类实现
- 与解析解的对比
- 不同学习率的影响分析
- 详细的可视化结果

**运行方式**:
```bash
python 04_linear_regression_gradient_descent.py
```

## 核心概念

### 梯度下降算法

梯度下降的参数更新规则：
```
θ = θ - α * ∇J(θ)
```

其中：
- `θ` 是待优化的参数
- `α` 是学习率，控制每次更新的步长
- `∇J(θ)` 是目标函数关于参数的梯度

### 三种梯度下降变种

| 方法 | 每次更新使用样本 | 优点 | 缺点 |
|------|------------------|------|------|
| **BGD** | 全量数据集 | 梯度精确，收敛稳定 | 大数据集下很慢 |
| **SGD** | 1个随机样本 | 更新快，能跳出局部最优 | 收敛不稳定 |
| **MBGD** | 1个mini-batch | 速度与稳定性平衡 | 增加batch size超参数 |

## 依赖库

```bash
pip install numpy matplotlib
```

## 使用建议

1. **学习顺序**: 建议按照文件编号顺序学习，从简单到复杂
2. **参数调整**: 可以修改学习率、迭代次数等参数观察效果
3. **可视化**: 每个文件都包含详细的可视化，有助于理解算法原理
4. **实验**: 尝试修改目标函数或数据集，观察算法的适应性

## 关键要点

1. **学习率的重要性**: 过大可能导致发散，过小收敛太慢
2. **收敛条件**: 可以使用固定迭代次数或损失变化阈值
3. **初始化**: 参数初始化会影响收敛速度和最终结果
4. **监控**: 通过损失函数曲线监控训练过程
5. **实际应用**: 小批量梯度下降是实际应用中的主流方法

## 扩展练习

1. 尝试实现动量梯度下降
2. 实现自适应学习率调整
3. 将算法应用到多项式回归
4. 实现正则化版本的梯度下降
5. 比较与其他优化算法（如Adam）的性能
