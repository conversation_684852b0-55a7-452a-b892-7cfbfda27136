# 极端参数设置的梯度下降比较 - 让差异更明显
# 目标：学习线性函数 y = 5x 的参数

import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def train_func(X, K):
    """训练函数：y = K * x"""
    return K * X

# 准备数据 - 使用更极端的参数设置
EXAMPLE_NUM = 50  # 减少样本数
BATCH_SIZE = 3    # 很小的批量大小
TRAIN_STEPS = 100 # 减少训练步数
LEARNING_RATE = 0.01  # 更大的学习率

# 生成训练数据
X_INPUT = np.arange(EXAMPLE_NUM) * 0.1
Y_OUTPUT_CORRECT = 5 * X_INPUT  # 真实函数 y = 5x

print(f"极端参数设置:")
print(f"数据集大小: {EXAMPLE_NUM}")
print(f"批量大小: {BATCH_SIZE}")
print(f"训练步数: {TRAIN_STEPS}")
print(f"学习率: {LEARNING_RATE} (比之前大100倍!)")
print(f"真实参数: K = 5")
print("-" * 50)

# BGD 实现
k_BGD = 0.0
k_BGD_RECORD = [k_BGD]
for step in range(TRAIN_STEPS):
    predictions = train_func(X_INPUT, k_BGD)
    errors = predictions - Y_OUTPUT_CORRECT
    gradient_sum = np.sum(errors * X_INPUT)
    k_BGD -= (LEARNING_RATE / EXAMPLE_NUM) * gradient_sum
    k_BGD_RECORD.append(k_BGD)

print(f"BGD 最终参数: K = {k_BGD:.5f}")

# SGD 实现
np.random.seed(42)
k_SGD = 0.0
k_SGD_RECORD = [k_SGD]
for step in range(TRAIN_STEPS):
    index = np.random.randint(len(X_INPUT))
    prediction = train_func(X_INPUT[index], k_SGD)
    error = prediction - Y_OUTPUT_CORRECT[index]
    gradient = error * X_INPUT[index]
    k_SGD -= LEARNING_RATE * gradient  # 注意：SGD不除以样本数
    k_SGD_RECORD.append(k_SGD)

print(f"SGD 最终参数: K = {k_SGD:.5f}")

# MBGD 实现
np.random.seed(42)
k_MBGD = 0.0
k_MBGD_RECORD = [k_MBGD]
for step in range(TRAIN_STEPS):
    if len(X_INPUT) <= BATCH_SIZE:
        batch_indices = np.arange(len(X_INPUT))
    else:
        start_idx = np.random.randint(len(X_INPUT) - BATCH_SIZE + 1)
        batch_indices = np.arange(start_idx, start_idx + BATCH_SIZE)
    
    X_batch = X_INPUT[batch_indices]
    y_batch = Y_OUTPUT_CORRECT[batch_indices]
    
    predictions = train_func(X_batch, k_MBGD)
    errors = predictions - y_batch
    gradient_sum = np.sum(errors * X_batch)
    k_MBGD -= (LEARNING_RATE / BATCH_SIZE) * gradient_sum
    k_MBGD_RECORD.append(k_MBGD)

print(f"MBGD 最终参数: K = {k_MBGD:.5f}")

# 可视化比较结果 - 突出差异
plt.figure(figsize=(16, 12))

# 主图：参数收敛过程
plt.subplot(2, 3, 1)
steps = np.arange(TRAIN_STEPS + 1)
plt.plot(steps, k_BGD_RECORD, 'r-', linewidth=4, label='BGD (平滑稳定)', alpha=0.9)
plt.plot(steps, k_SGD_RECORD, 'g-', linewidth=2, label='SGD (剧烈震荡)', alpha=0.8)
plt.plot(steps, k_MBGD_RECORD, 'b-', linewidth=3, label='MBGD (适中震荡)', alpha=0.9)
plt.axhline(y=5, color='purple', linestyle='--', linewidth=3, label='真实值 (K=5)')
plt.xlabel('训练步数')
plt.ylabel('参数 K')
plt.title('收敛过程对比\n(大学习率下的明显差异)')
plt.legend()
plt.grid(True, alpha=0.3)

# 最后20步的放大图
plt.subplot(2, 3, 2)
start_idx = max(0, TRAIN_STEPS - 20)
steps_zoom = steps[start_idx:]
plt.plot(steps_zoom, k_BGD_RECORD[start_idx:], 'r-', linewidth=4, label='BGD')
plt.plot(steps_zoom, k_SGD_RECORD[start_idx:], 'g-', linewidth=2, label='SGD')
plt.plot(steps_zoom, k_MBGD_RECORD[start_idx:], 'b-', linewidth=3, label='MBGD')
plt.axhline(y=5, color='purple', linestyle='--', linewidth=2, label='真实值')
plt.xlabel('训练步数')
plt.ylabel('参数 K')
plt.title('最后20步放大图')
plt.legend()
plt.grid(True, alpha=0.3)

# 误差分析
plt.subplot(2, 3, 3)
bgd_errors = np.abs(np.array(k_BGD_RECORD) - 5)
sgd_errors = np.abs(np.array(k_SGD_RECORD) - 5)
mbgd_errors = np.abs(np.array(k_MBGD_RECORD) - 5)

plt.plot(steps, bgd_errors, 'r-', linewidth=3, label='BGD')
plt.plot(steps, sgd_errors, 'g-', linewidth=2, label='SGD')
plt.plot(steps, mbgd_errors, 'b-', linewidth=3, label='MBGD')
plt.xlabel('训练步数')
plt.ylabel('绝对误差')
plt.title('参数误差变化')
plt.legend()
plt.grid(True, alpha=0.3)

# 方差分析
plt.subplot(2, 3, 4)
window_size = 5
bgd_variance = [np.var(k_BGD_RECORD[max(0, i-window_size):i+1]) for i in range(len(k_BGD_RECORD))]
sgd_variance = [np.var(k_SGD_RECORD[max(0, i-window_size):i+1]) for i in range(len(k_SGD_RECORD))]
mbgd_variance = [np.var(k_MBGD_RECORD[max(0, i-window_size):i+1]) for i in range(len(k_MBGD_RECORD))]

plt.plot(steps, bgd_variance, 'r-', linewidth=3, label='BGD (方差最小)')
plt.plot(steps, sgd_variance, 'g-', linewidth=2, label='SGD (方差最大)')
plt.plot(steps, mbgd_variance, 'b-', linewidth=3, label='MBGD (方差适中)')
plt.xlabel('训练步数')
plt.ylabel('方差')
plt.title('参数更新的方差对比')
plt.legend()
plt.grid(True, alpha=0.3)

# 梯度大小对比
plt.subplot(2, 3, 5)
# 计算每步的梯度大小（简化版）
bgd_gradients = [abs(k_BGD_RECORD[i+1] - k_BGD_RECORD[i]) for i in range(len(k_BGD_RECORD)-1)]
sgd_gradients = [abs(k_SGD_RECORD[i+1] - k_SGD_RECORD[i]) for i in range(len(k_SGD_RECORD)-1)]
mbgd_gradients = [abs(k_MBGD_RECORD[i+1] - k_MBGD_RECORD[i]) for i in range(len(k_MBGD_RECORD)-1)]

plt.plot(bgd_gradients, 'r-', linewidth=3, label='BGD')
plt.plot(sgd_gradients, 'g-', linewidth=2, label='SGD')
plt.plot(mbgd_gradients, 'b-', linewidth=3, label='MBGD')
plt.xlabel('训练步数')
plt.ylabel('参数更新幅度')
plt.title('每步参数更新幅度')
plt.legend()
plt.grid(True, alpha=0.3)

# 收敛速度对比
plt.subplot(2, 3, 6)
# 计算距离真实值的距离
bgd_distance = [abs(k - 5) for k in k_BGD_RECORD]
sgd_distance = [abs(k - 5) for k in k_SGD_RECORD]
mbgd_distance = [abs(k - 5) for k in k_MBGD_RECORD]

plt.semilogy(steps, bgd_distance, 'r-', linewidth=3, label='BGD')
plt.semilogy(steps, sgd_distance, 'g-', linewidth=2, label='SGD')
plt.semilogy(steps, mbgd_distance, 'b-', linewidth=3, label='MBGD')
plt.xlabel('训练步数')
plt.ylabel('距离真实值的距离 (对数尺度)')
plt.title('收敛速度对比')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 打印详细分析
print("\n" + "="*60)
print("详细分析:")
print("="*60)
print(f"BGD: 最稳定，方差 = {np.var(k_BGD_RECORD):.6f}")
print(f"SGD: 最震荡，方差 = {np.var(k_SGD_RECORD):.6f}")
print(f"MBGD: 适中，方差 = {np.var(k_MBGD_RECORD):.6f}")
print(f"\nSGD的方差是BGD的 {np.var(k_SGD_RECORD)/np.var(k_BGD_RECORD):.1f} 倍!")
