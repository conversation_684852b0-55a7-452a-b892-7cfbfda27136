"""
批量梯度下降(BGD)、随机梯度下降(SGD)和小批量梯度下降(MBGD)的比较
目标：学习线性函数 y = 5x 的参数
"""

import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def train_func(X, K):
    """训练函数：y = K * x"""
    return K * X

# 1. 准备数据
EXAMPLE_NUM = 100
BATCH_SIZE = 10
TRAIN_STEPS = 150
LEARNING_RATE = 0.0001

# 生成训练数据
X_INPUT = np.arange(EXAMPLE_NUM) * 0.1
Y_OUTPUT_CORRECT = 5 * X_INPUT  # 真实函数 y = 5x

print(f"数据集大小: {EXAMPLE_NUM}")
print(f"批量大小: {BATCH_SIZE}")
print(f"训练步数: {TRAIN_STEPS}")
print(f"学习率: {LEARNING_RATE}")
print(f"真实参数: K = 5")
print("-" * 40)

# 2. BGD 实现
print("开始BGD训练...")
k_BGD = 0.0
k_BGD_RECORD = [k_BGD]
for step in range(TRAIN_STEPS):
    # 计算所有样本的梯度
    predictions = train_func(X_INPUT, k_BGD)
    errors = predictions - Y_OUTPUT_CORRECT
    gradient_sum = np.sum(errors * X_INPUT)

    # 更新参数（注意要除以样本总数）
    k_BGD -= (LEARNING_RATE / EXAMPLE_NUM) * gradient_sum
    k_BGD_RECORD.append(k_BGD)

print(f"BGD 最终参数: K = {k_BGD:.5f}")

# 3. SGD 实现
print("开始SGD训练...")
np.random.seed(42)  # 设置随机种子以便复现
k_SGD = 0.0
k_SGD_RECORD = [k_SGD]
for step in range(TRAIN_STEPS):
    # 随机选择一个样本
    index = np.random.randint(len(X_INPUT))
    prediction = train_func(X_INPUT[index], k_SGD)
    error = prediction - Y_OUTPUT_CORRECT[index]
    gradient = error * X_INPUT[index]

    # 更新参数
    k_SGD -= LEARNING_RATE * gradient
    k_SGD_RECORD.append(k_SGD)

print(f"SGD 最终参数: K = {k_SGD:.5f}")

# 4. MBGD 实现
print("开始MBGD训练...")
np.random.seed(42)  # 设置随机种子以便复现
k_MBGD = 0.0
k_MBGD_RECORD = [k_MBGD]
for step in range(TRAIN_STEPS):
    # 随机选择一个批次
    if len(X_INPUT) <= BATCH_SIZE:
        batch_indices = np.arange(len(X_INPUT))
    else:
        start_idx = np.random.randint(len(X_INPUT) - BATCH_SIZE + 1)
        batch_indices = np.arange(start_idx, start_idx + BATCH_SIZE)

    X_batch = X_INPUT[batch_indices]
    y_batch = Y_OUTPUT_CORRECT[batch_indices]

    # 计算批次梯度
    predictions = train_func(X_batch, k_MBGD)
    errors = predictions - y_batch
    gradient_sum = np.sum(errors * X_batch)

    # 更新参数（注意要除以批量大小）
    k_MBGD -= (LEARNING_RATE / BATCH_SIZE) * gradient_sum
    k_MBGD_RECORD.append(k_MBGD)

print(f"MBGD 最终参数: K = {k_MBGD:.5f}")

def visualize_comparison(k_bgd_record, k_sgd_record, k_mbgd_record, train_steps):
    """可视化比较结果"""
    plt.figure(figsize=(12, 8))
    
    # 主图：参数收敛过程
    plt.subplot(2, 2, 1)
    steps = np.arange(train_steps + 1)
    plt.plot(steps, k_bgd_record, 'r-', linewidth=2, label='BGD (批量梯度下降)')
    plt.plot(steps, k_sgd_record, 'g-', linewidth=1, alpha=0.7, label='SGD (随机梯度下降)')
    plt.plot(steps, k_mbgd_record, 'b-', linewidth=2, label='MBGD (小批量梯度下降)')
    plt.axhline(y=5, color='purple', linestyle='--', linewidth=2, label='真实值 (K=5)')
    plt.xlabel('训练步数')
    plt.ylabel('参数 K')
    plt.title('三种梯度下降方法的收敛过程')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 局部放大图（最后50步）
    plt.subplot(2, 2, 2)
    start_idx = max(0, train_steps - 50)
    steps_zoom = steps[start_idx:]
    plt.plot(steps_zoom, k_bgd_record[start_idx:], 'r-', linewidth=2, label='BGD')
    plt.plot(steps_zoom, k_sgd_record[start_idx:], 'g-', linewidth=1, alpha=0.7, label='SGD')
    plt.plot(steps_zoom, k_mbgd_record[start_idx:], 'b-', linewidth=2, label='MBGD')
    plt.axhline(y=5, color='purple', linestyle='--', linewidth=2, label='真实值')
    plt.xlabel('训练步数')
    plt.ylabel('参数 K')
    plt.title('收敛过程（最后50步）')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 误差分析
    plt.subplot(2, 2, 3)
    bgd_errors = np.abs(np.array(k_bgd_record) - 5)
    sgd_errors = np.abs(np.array(k_sgd_record) - 5)
    mbgd_errors = np.abs(np.array(k_mbgd_record) - 5)
    
    plt.semilogy(steps, bgd_errors, 'r-', linewidth=2, label='BGD')
    plt.semilogy(steps, sgd_errors, 'g-', linewidth=1, alpha=0.7, label='SGD')
    plt.semilogy(steps, mbgd_errors, 'b-', linewidth=2, label='MBGD')
    plt.xlabel('训练步数')
    plt.ylabel('绝对误差 (对数尺度)')
    plt.title('参数误差变化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 方差分析（最后50步）
    plt.subplot(2, 2, 4)
    window_size = 10
    if len(k_bgd_record) >= window_size:
        bgd_variance = [np.var(k_bgd_record[max(0, i-window_size):i+1]) for i in range(len(k_bgd_record))]
        sgd_variance = [np.var(k_sgd_record[max(0, i-window_size):i+1]) for i in range(len(k_sgd_record))]
        mbgd_variance = [np.var(k_mbgd_record[max(0, i-window_size):i+1]) for i in range(len(k_mbgd_record))]
        
        plt.plot(steps, bgd_variance, 'r-', linewidth=2, label='BGD')
        plt.plot(steps, sgd_variance, 'g-', linewidth=1, alpha=0.7, label='SGD')
        plt.plot(steps, mbgd_variance, 'b-', linewidth=2, label='MBGD')
        plt.xlabel('训练步数')
        plt.ylabel('方差')
        plt.title('参数更新的方差')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def analyze_performance():
    """性能分析"""
    print("\n" + "=" * 60)
    print("性能分析")
    print("=" * 60)
    
    methods = ['BGD', 'SGD', 'MBGD']
    characteristics = {
        'BGD': {
            '每次更新使用样本': '全量数据集',
            '收敛路径': '平滑，直接朝向最优解',
            '计算速度': '慢',
            '内存占用': '大',
            '优点': '梯度精确，收敛稳定',
            '缺点': '大数据集下非常慢'
        },
        'SGD': {
            '每次更新使用样本': '1个 (随机)',
            '收敛路径': '震荡，噪声大',
            '计算速度': '快',
            '内存占用': '小',
            '优点': '更新快，能跳出局部最优',
            '缺点': '收敛不稳定，需要调学习率'
        },
        'MBGD': {
            '每次更新使用样本': '1个 mini-batch',
            '收敛路径': '相对平滑，有少量噪声',
            '计算速度': '中等，高效利用硬件',
            '内存占用': '中等',
            '优点': '速度与稳定性的最佳平衡',
            '缺点': '增加了batch size超参数'
        }
    }
    
    for method in methods:
        print(f"\n{method}:")
        for key, value in characteristics[method].items():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    print("=" * 60)
    print("梯度下降方法比较：BGD vs SGD vs MBGD")
    print("=" * 60)
    
    # 运行比较
    k_bgd_record, k_sgd_record, k_mbgd_record = compare_gradient_descent_methods()
    
    # 可视化结果
    visualize_comparison(k_bgd_record, k_sgd_record, k_mbgd_record, 150)
    
    # 性能分析
    analyze_performance()
