"""
梯度下降算法演示主程序
整合所有梯度下降实现的演示
"""

import sys
import os

# 添加当前目录到路径，以便导入其他模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def show_menu():
    """显示菜单"""
    print("\n" + "=" * 60)
    print("梯度下降算法演示")
    print("=" * 60)
    print("1. 一维函数梯度下降演示")
    print("2. 二维函数梯度下降演示") 
    print("3. BGD vs SGD vs MBGD 比较")
    print("4. 线性回归梯度下降演示")
    print("5. 运行所有演示")
    print("0. 退出")
    print("=" * 60)

def run_1d_demo():
    """运行一维梯度下降演示"""
    print("\n正在运行一维函数梯度下降演示...")
    try:
        from demo_1d_gradient_descent import gradient_descent_1d, visualize_1d_gradient_descent
        theta_history, iter_num, final_loss = gradient_descent_1d()
        visualize_1d_gradient_descent(theta_history, iter_num, final_loss)
        print("一维梯度下降演示完成！")
    except ImportError:
        print("导入模块失败，直接运行文件...")
        os.system("python 01_1d_gradient_descent.py")

def run_2d_demo():
    """运行二维梯度下降演示"""
    print("\n正在运行二维函数梯度下降演示...")
    try:
        from demo_2d_gradient_descent import gradient_descent_2d, visualize_2d_gradient_descent
        theta_history, iter_num, final_loss = gradient_descent_2d()
        visualize_2d_gradient_descent(theta_history, iter_num, final_loss)
        print("二维梯度下降演示完成！")
    except ImportError:
        print("导入模块失败，直接运行文件...")
        os.system("python 02_2d_gradient_descent.py")

def run_comparison_demo():
    """运行梯度下降方法比较演示"""
    print("\n正在运行BGD vs SGD vs MBGD比较演示...")
    try:
        from demo_bgd_sgd_mbgd_comparison import compare_gradient_descent_methods, visualize_comparison
        k_bgd, k_sgd, k_mbgd = compare_gradient_descent_methods()
        visualize_comparison(k_bgd, k_sgd, k_mbgd, 150)
        print("梯度下降方法比较演示完成！")
    except ImportError:
        print("导入模块失败，直接运行文件...")
        os.system("python 03_bgd_sgd_mbgd_comparison.py")

def run_linear_regression_demo():
    """运行线性回归梯度下降演示"""
    print("\n正在运行线性回归梯度下降演示...")
    try:
        from demo_linear_regression_gradient_descent import generate_data, LinearRegressionGD, visualize_results, analytical_solution
        
        # 生成数据
        X, y, true_theta0, true_theta1 = generate_data()
        
        # 训练模型
        model = LinearRegressionGD(learning_rate=0.01, n_epochs=1000)
        model.fit(X, y)
        
        # 解析解对比
        theta_analytical = analytical_solution(X, y)
        print(f"解析解参数: θ0 = {theta_analytical[0]:.6f}, θ1 = {theta_analytical[1]:.6f}")
        
        # 可视化
        visualize_results(X, y, model, true_theta0, true_theta1)
        print("线性回归梯度下降演示完成！")
    except ImportError:
        print("导入模块失败，直接运行文件...")
        os.system("python 04_linear_regression_gradient_descent.py")

def run_all_demos():
    """运行所有演示"""
    print("\n正在运行所有梯度下降演示...")
    
    demos = [
        ("一维函数梯度下降", "01_1d_gradient_descent.py"),
        ("二维函数梯度下降", "02_2d_gradient_descent.py"),
        ("BGD vs SGD vs MBGD比较", "03_bgd_sgd_mbgd_comparison.py"),
        ("线性回归梯度下降", "04_linear_regression_gradient_descent.py")
    ]
    
    for name, filename in demos:
        print(f"\n{'='*40}")
        print(f"正在运行: {name}")
        print(f"{'='*40}")
        
        try:
            os.system(f"python {filename}")
            print(f"{name} 演示完成！")
        except Exception as e:
            print(f"运行 {name} 时出错: {e}")
        
        input("\n按回车键继续下一个演示...")

def show_theory():
    """显示理论说明"""
    print("\n" + "=" * 60)
    print("梯度下降理论说明")
    print("=" * 60)
    
    theory_text = """
    梯度下降是一种优化算法，用于找到函数的最小值。

    核心思想：
    - 沿着函数梯度的反方向移动
    - 梯度指向函数增长最快的方向
    - 反方向就是函数下降最快的方向

    更新公式：
    θ = θ - α * ∇J(θ)

    其中：
    - θ: 待优化的参数
    - α: 学习率（步长）
    - ∇J(θ): 目标函数J关于θ的梯度

    三种变种：
    1. 批量梯度下降(BGD): 使用全部数据计算梯度
    2. 随机梯度下降(SGD): 使用单个样本计算梯度  
    3. 小批量梯度下降(MBGD): 使用小批量数据计算梯度

    关键参数：
    - 学习率: 控制收敛速度，过大可能发散，过小收敛慢
    - 迭代次数: 控制训练时长
    - 批量大小: 影响梯度估计的准确性和计算效率
    """
    
    print(theory_text)
    input("\n按回车键返回主菜单...")

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择要运行的演示 (0-5): ").strip()
            
            if choice == '0':
                print("感谢使用梯度下降演示程序！")
                break
            elif choice == '1':
                run_1d_demo()
            elif choice == '2':
                run_2d_demo()
            elif choice == '3':
                run_comparison_demo()
            elif choice == '4':
                run_linear_regression_demo()
            elif choice == '5':
                run_all_demos()
            elif choice.lower() == 'h' or choice.lower() == 'help':
                show_theory()
            else:
                print("无效选择，请输入0-5之间的数字")
                
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")
            
        input("\n按回车键继续...")

if __name__ == "__main__":
    print("梯度下降算法演示程序")
    print("提示: 输入 'h' 或 'help' 查看理论说明")
    main()
