# 线性回归的梯度下降实现
# 使用批量梯度下降(BGD)解决线性回归问题

import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 生成训练数据
np.random.seed(42)

# 构造特征X
X = np.arange(0, 5, 0.5).reshape(-1, 1)

# 构造目标y，真实关系为 y = 2.3x + 0.8 + noise
true_theta0 = 0.8  # 截距
true_theta1 = 2.3  # 斜率
noise = np.random.normal(0, 0.5, size=X.shape[0])
y = true_theta1 * X.flatten() + true_theta0 + noise

print(f"真实参数: θ0 = {true_theta0}, θ1 = {true_theta1}")
print(f"数据集大小: {len(X)}")

# 添加截距项 (x0 = 1)
X_b = np.c_[np.ones((X.shape[0], 1)), X]

# 初始化参数
theta = np.random.randn(X_b.shape[1], 1)
learning_rate = 0.01
n_epochs = 1000
loss_history = []

# 获取样本数量
m = len(X_b)

print(f"开始训练，学习率: {learning_rate}, 迭代次数: {n_epochs}")
print(f"初始参数: {theta.flatten()}")

# 梯度下降迭代
for epoch in range(n_epochs):
    # 1. 计算预测值
    y_hat = X_b.dot(theta)

    # 2. 计算损失函数 (MSE)
    loss = (1/m) * np.sum((y_hat - y.reshape(-1, 1)) ** 2)
    loss_history.append(loss)

    # 3. 计算梯度
    gradients = (2/m) * X_b.T.dot(y_hat - y.reshape(-1, 1))

    # 4. 更新参数
    theta = theta - learning_rate * gradients

    # 每100次迭代打印一次结果
    if (epoch + 1) % 100 == 0:
        print(f"第{epoch + 1}次迭代: 损失 = {loss:.6f}, 参数 = {theta.flatten()}")

print(f"\n训练完成！最终参数: {theta.flatten()}")

# 解析解对比
theta_analytical = np.linalg.inv(X_b.T.dot(X_b)).dot(X_b.T).dot(y)
print(f"解析解参数: θ0 = {theta_analytical[0]:.6f}, θ1 = {theta_analytical[1]:.6f}")

gradient_params = theta.flatten()
print(f"梯度下降参数: θ0 = {gradient_params[0]:.6f}, θ1 = {gradient_params[1]:.6f}")
print(f"参数差异: Δθ0 = {abs(theta_analytical[0] - gradient_params[0]):.6f}, "
      f"Δθ1 = {abs(theta_analytical[1] - gradient_params[1]):.6f}")

# 可视化结果
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# 1. 数据和拟合结果
ax1 = axes[0, 0]
ax1.scatter(X, y, color='blue', alpha=0.6, label='训练数据')

# 预测线
X_plot = np.linspace(X.min(), X.max(), 100).reshape(-1, 1)
X_plot_b = np.c_[np.ones((X_plot.shape[0], 1)), X_plot]
y_pred = X_plot_b.dot(theta)
ax1.plot(X_plot, y_pred, 'r-', linewidth=2, label='梯度下降拟合')

# 真实线
y_true = true_theta1 * X_plot.flatten() + true_theta0
ax1.plot(X_plot, y_true, 'g--', linewidth=2, label='真实关系')

learned_params = theta.flatten()
ax1.set_title(f'线性回归拟合结果\n学习到的参数: θ0={learned_params[0]:.3f}, θ1={learned_params[1]:.3f}')
ax1.set_xlabel('X')
ax1.set_ylabel('y')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 2. 损失函数变化
ax2 = axes[0, 1]
ax2.plot(loss_history, 'b-', linewidth=2)
ax2.set_title('损失函数变化')
ax2.set_xlabel('迭代次数')
ax2.set_ylabel('MSE损失')
ax2.grid(True, alpha=0.3)

# 3. 损失函数变化（对数尺度）
ax3 = axes[1, 0]
ax3.semilogy(loss_history, 'b-', linewidth=2)
ax3.set_title('损失函数变化（对数尺度）')
ax3.set_xlabel('迭代次数')
ax3.set_ylabel('MSE损失 (对数尺度)')
ax3.grid(True, alpha=0.3)

# 4. 参数对比
ax4 = axes[1, 1]
ax4.text(0.1, 0.8, f'最终参数:', fontsize=12, transform=ax4.transAxes)
ax4.text(0.1, 0.7, f'θ0 = {learned_params[0]:.6f}', fontsize=11, transform=ax4.transAxes)
ax4.text(0.1, 0.6, f'θ1 = {learned_params[1]:.6f}', fontsize=11, transform=ax4.transAxes)
ax4.text(0.1, 0.4, f'真实参数:', fontsize=12, transform=ax4.transAxes)
ax4.text(0.1, 0.3, f'θ0 = {true_theta0:.6f}', fontsize=11, transform=ax4.transAxes)
ax4.text(0.1, 0.2, f'θ1 = {true_theta1:.6f}', fontsize=11, transform=ax4.transAxes)
ax4.text(0.1, 0.05, f'最终损失: {loss_history[-1]:.6f}', fontsize=11, transform=ax4.transAxes)
ax4.set_title('参数对比')
ax4.axis('off')

plt.tight_layout()
plt.show()

# 比较不同学习率的影响
learning_rates = [0.001, 0.01, 0.1, 0.5]

plt.figure(figsize=(15, 10))

for i, lr in enumerate(learning_rates):
    plt.subplot(2, 2, i+1)

    # 重新训练模型
    theta_test = np.random.randn(X_b.shape[1], 1)
    loss_history_test = []

    for epoch in range(500):
        y_hat = X_b.dot(theta_test)
        loss = (1/m) * np.sum((y_hat - y.reshape(-1, 1)) ** 2)
        loss_history_test.append(loss)
        gradients = (2/m) * X_b.T.dot(y_hat - y.reshape(-1, 1))
        theta_test = theta_test - lr * gradients

    # 绘制损失函数
    plt.plot(loss_history_test, linewidth=2)
    plt.title(f'学习率 = {lr}')
    plt.xlabel('迭代次数')
    plt.ylabel('MSE损失')
    plt.grid(True, alpha=0.3)

    # 添加最终参数信息
    final_params = theta_test.flatten()
    plt.text(0.02, 0.98, f'θ0={final_params[0]:.3f}\nθ1={final_params[1]:.3f}',
            transform=plt.gca().transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

plt.tight_layout()
plt.show()
